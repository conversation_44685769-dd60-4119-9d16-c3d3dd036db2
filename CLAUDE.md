# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.Communicate in Chinese.Comment code in Chinese.

## Project Overview

This project, named "New API", is a gateway for large language models and an AI asset management system. It's a fork of "One API" with additional features. The backend is written in Go, and the frontend is a React application located in the `web` directory.

## Common Commands

-   **Build frontend and start backend dev server:**
    ```bash
    make
    ```
-   **Build frontend only:**
    ```bash
    make build-frontend
    ```
    This command runs `bun install` and `bun run build` in the `web` directory.

-   **Start backend dev server only:**
    ```bash
    make start-backend
    ```
    This command runs `go run main.go` in the root directory.

-   **Run the application with Docker Compose (recommended for production):**
    ```bash
    docker-compose up -d
    ```

## Architecture

-   **Backend:** The main application logic is in Go. The entry point is `main.go`.
-   **Frontend:** The frontend is a React application located in the `web` directory.
-   **Database:** The application can use SQLite (default), MySQL, or PostgreSQL. For Docker deployments, the data directory `/data` should be mounted to persist the SQLite database.
-   **Caching:** The application supports Redis for caching. If Redis is configured, it's used as the cache. Otherwise, an in-memory cache can be enabled.
-   **Configuration:** The application is configured through environment variables.

## Important Environment Variables

-   `SESSION_SECRET`: Required for multi-instance deployments to ensure consistent login sessions.
-   `CRYPTO_SECRET`: Required for multi-instance deployments using a shared Redis instance to encrypt/decrypt Redis content.
-   `REDIS_CONN_STRING`: Connection string for Redis to enable caching.
-   `MEMORY_CACHE_ENABLED`: Set to `true` to enable in-memory caching if Redis is not used.
-   `SQL_DSN`: The Data Source Name for connecting to a MySQL or PostgreSQL database.

## Communication Language

- Please communicate with me in Chinese.
